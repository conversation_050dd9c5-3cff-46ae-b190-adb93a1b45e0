package com.ruoyi.expense.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.ruoyi.expense.domain.BillUploadDTO;
import com.ruoyi.expense.service.IBillUploadService;
import com.ruoyi.framework.web.service.DictService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 账单上传服务实现类
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
@Service
public class BillUploadServiceImpl implements IBillUploadService {
    
    private static final Logger log = LoggerFactory.getLogger(BillUploadServiceImpl.class);
    
    @Autowired
    private DictService dictService;
    
    @Value("${expense.receive.base-url}")
    private String receiveBaseUrl;
    
    @Value("${expense.receive.path:/expense/receive}")
    private String receivePath;
    
    // 账单相关数据字段名
    private static final String[] BILL_HEADER_FIELDS = {
        "费用类型", "计费周期", "划账部门", "含税总价", "不含税总价"
    };
    
    // 费用明细字段名
    private static final String[] DETAIL_HEADER_FIELDS = {
        "费用名称", "编号", "品牌", "具体规格", "备注", "费用变动情况", "划账部门", 
        "数量", "含税单价", "不含税单价", "含税单行总价", "不含税单行总价"
    };
    
    // 费用明细必填字段
    private static final String[] DETAIL_REQUIRED_FIELDS = {
        "费用名称", "品牌", "数量", "含税单价", "不含税单价", "含税单行总价", "不含税单行总价"
    };
    
    // 字典类型常量
    private static final String EXPENSE_TYPE_DICT = "expense_type";
    private static final String EXPENSE_INSTITUDE_DICT = "expense_institude";
    
    @Override
    public String processBillUpload(BillUploadDTO uploadDTO) {
        log.info("开始处理账单上传，发布人：{}，账单数量：{}", uploadDTO.getPublisher(), uploadDTO.getBillCount());
        
        try {
            // 验证文件格式
            if (!validateExcelFile(uploadDTO.getFile())) {
                return "文件格式不正确，请上传xlsx格式的Excel文件";
            }
            
            // 验证Excel内容格式
            String contentValidation = validateExcelContent(uploadDTO.getFile());
            if (!contentValidation.equals("验证通过")) {
                return contentValidation;
            }
            
            // 解析Excel文件并发送数据
            String result = parseExcelFile(uploadDTO.getFile(), uploadDTO);
            
            log.info("账单上传处理完成，结果：{}", result);
            return result;
            
        } catch (Exception e) {
            log.error("处理账单上传时发生错误", e);
            return "处理账单上传时发生错误：" + e.getMessage();
        }
    }
    
    @Override
    public boolean validateExcelFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            log.warn("上传文件为空");
            return false;
        }
        
        String filename = file.getOriginalFilename();
        if (filename == null || !filename.toLowerCase().endsWith(".xlsx")) {
            log.warn("文件格式不正确，文件名：{}", filename);
            return false;
        }
        
        // 检查文件大小（50MB限制）
        if (file.getSize() > 50 * 1024 * 1024) {
            log.warn("文件大小超过限制：{} bytes", file.getSize());
            return false;
        }
        
        return true;
    }
    
    @Override
    public String validateExcelContent(MultipartFile file) {
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            int totalSheets = workbook.getNumberOfSheets();
            log.info("开始验证Excel内容，共{}个Sheet", totalSheets);
            
            for (int sheetIndex = 0; sheetIndex < totalSheets; sheetIndex++) {
                Sheet sheet = workbook.getSheetAt(sheetIndex);
                String sheetName = sheet.getSheetName();
                log.info("验证Sheet：{}", sheetName);
                
                String sheetValidation = validateSheetContent(sheet, sheetName);
                if (!sheetValidation.equals("验证通过")) {
                    return "Sheet [" + sheetName + "] " + sheetValidation;
                }
            }
            
            return "验证通过";
            
        } catch (IOException e) {
            log.error("读取Excel文件时发生错误", e);
            return "读取Excel文件失败：" + e.getMessage();
        } catch (Exception e) {
            log.error("验证Excel文件时发生错误", e);
            return "验证Excel文件失败：" + e.getMessage();
        }
    }
    
    /**
     * 验证单个Sheet的内容格式
     */
    private String validateSheetContent(Sheet sheet, String sheetName) {
        if (sheet.getLastRowNum() < 3) {
            return "格式错误：至少需要4行数据（账单数据2行 + 明细数据至少2行）";
        }
        
        // 验证第一行（账单字段名）
        Row firstRow = sheet.getRow(0);
        if (firstRow == null) {
            return "格式错误：第一行不能为空";
        }
        
        for (int i = 0; i < BILL_HEADER_FIELDS.length; i++) {
            Cell cell = firstRow.getCell(i);
            String cellValue = getCellStringValue(cell);
            if (!BILL_HEADER_FIELDS[i].equals(cellValue)) {
                return String.format("格式错误：第一行第%d列应为'%s'，实际为'%s'", 
                    i + 1, BILL_HEADER_FIELDS[i], cellValue);
            }
        }
        
        // 验证第二行（账单数据）
        Row secondRow = sheet.getRow(1);
        if (secondRow == null) {
            return "格式错误：第二行不能为空";
        }
        
        for (int i = 0; i < BILL_HEADER_FIELDS.length; i++) {
            Cell cell = secondRow.getCell(i);
            String cellValue = getCellStringValue(cell);
            if (cellValue.trim().isEmpty()) {
                return String.format("格式错误：第二行第%d列（%s）不能为空", 
                    i + 1, BILL_HEADER_FIELDS[i]);
            }
        }
        
        // 验证第三行（明细字段名）
        Row thirdRow = sheet.getRow(2);
        if (thirdRow == null) {
            return "格式错误：第三行不能为空";
        }
        
        for (int i = 0; i < DETAIL_HEADER_FIELDS.length; i++) {
            Cell cell = thirdRow.getCell(i);
            String cellValue = getCellStringValue(cell);
            if (!DETAIL_HEADER_FIELDS[i].equals(cellValue)) {
                return String.format("格式错误：第三行第%d列应为'%s'，实际为'%s'", 
                    i + 1, DETAIL_HEADER_FIELDS[i], cellValue);
            }
        }
        
        // 验证第四行及以后的明细数据
        for (int rowIndex = 3; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null || isEmptyRow(row)) {
                continue; // 跳过空行
            }
            
            String rowValidation = validateDetailRow(row, rowIndex + 1);
            if (!rowValidation.equals("验证通过")) {
                return rowValidation;
            }
        }
        
        // 新增校验：验证账单部门与明细部门一致性
        String deptValidation = validateDepartmentConsistency(sheet);
        if (!deptValidation.equals("验证通过")) {
            return deptValidation;
        }
        
        // 新增校验：验证账单总价与明细价格之和一致性
        String priceValidation = validatePriceConsistency(sheet);
        if (!priceValidation.equals("验证通过")) {
            return priceValidation;
        }
        
        // 新增校验：验证字典值
        String dictValidation = validateDictionaryValues(sheet);
        if (!dictValidation.equals("验证通过")) {
            return dictValidation;
        }
        
        // 新增校验：验证计费周期格式
        String billingCycleValidation = validateBillingCycleFormat(sheet);
        if (!billingCycleValidation.equals("验证通过")) {
            return billingCycleValidation;
        }
        
        // 新增校验：验证明细价格计算
        String detailPriceValidation = validateDetailPriceCalculation(sheet);
        if (!detailPriceValidation.equals("验证通过")) {
            return detailPriceValidation;
        }
        
        return "验证通过";
    }
    
    /**
     * 验证明细数据行
     */
    private String validateDetailRow(Row row, int rowNumber) {
        // 检查必填字段
        for (String requiredField : DETAIL_REQUIRED_FIELDS) {
            int columnIndex = getColumnIndex(requiredField);
            if (columnIndex >= 0) {
                Cell cell = row.getCell(columnIndex);
                String cellValue = getCellStringValue(cell);
                if (cellValue.trim().isEmpty()) {
                    return String.format("格式错误：第%d行'%s'字段不能为空", rowNumber, requiredField);
                }
            }
        }
        
        return "验证通过";
    }
    
    /**
     * 获取字段在明细表头中的列索引
     */
    private int getColumnIndex(String fieldName) {
        for (int i = 0; i < DETAIL_HEADER_FIELDS.length; i++) {
            if (DETAIL_HEADER_FIELDS[i].equals(fieldName)) {
                return i;
            }
        }
        return -1;
    }
    
    /**
     * 验证账单部门与明细部门一致性
     */
    private String validateDepartmentConsistency(Sheet sheet) {
        // 获取账单中的划账部门（第二行第三列）
        Row billRow = sheet.getRow(1);
        if (billRow == null) {
            return "格式错误：账单数据行不存在";
        }
        
        String billDepartment = getCellStringValue(billRow.getCell(2)).trim();
        if (billDepartment.isEmpty()) {
            return "格式错误：账单划账部门不能为空";
        }
        
        // 检查所有明细行的划账部门（第四行开始，第七列）
        for (int rowIndex = 3; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null || isEmptyRow(row)) {
                continue; // 跳过空行
            }
            
            String detailDepartment = getCellStringValue(row.getCell(6)).trim();
            if (!detailDepartment.isEmpty() && !billDepartment.equals(detailDepartment)) {
                return String.format("部门不一致错误：账单划账部门为'%s'，但第%d行明细划账部门为'%s'", 
                    billDepartment, rowIndex + 1, detailDepartment);
            }
        }
        
        return "验证通过";
    }
    
    /**
     * 验证账单总价与明细价格之和一致性
     */
    private String validatePriceConsistency(Sheet sheet) {
        // 获取账单中的含税总价和不含税总价（第二行第四、五列）
        Row billRow = sheet.getRow(1);
        if (billRow == null) {
            return "格式错误：账单数据行不存在";
        }
        
        // 使用相同的舍入逻辑确保验证与处理一致
        double billTaxInclusiveTotal = Double.parseDouble(formatPriceValue(getCellNumericValue(billRow.getCell(3))));
        double billTaxExclusiveTotal = Double.parseDouble(formatPriceValue(getCellNumericValue(billRow.getCell(4))));
        
        // 计算所有明细行的价格之和
        double detailTaxInclusiveSum = 0.0;
        double detailTaxExclusiveSum = 0.0;
        
        for (int rowIndex = 3; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null || isEmptyRow(row)) {
                continue; // 跳过空行
            }
            
            // 含税单行总价（第十一列），使用相同的舍入逻辑
            double taxInclusiveRowTotal = Double.parseDouble(formatPriceValue(getCellNumericValue(row.getCell(10))));
            detailTaxInclusiveSum += taxInclusiveRowTotal;
            
            // 不含税单行总价（第十二列），使用相同的舍入逻辑
            double taxExclusiveRowTotal = Double.parseDouble(formatPriceValue(getCellNumericValue(row.getCell(11))));
            detailTaxExclusiveSum += taxExclusiveRowTotal;
        }
        
        // 使用合理的容差值进行比较，避免浮点数精度问题
        // 容差设定为0.02（2分钱），这对于财务数据来说是合理的精度
        final double TOLERANCE = 0.02;
        
        // 比较含税总价
        double taxInclusiveDifference = Math.abs(billTaxInclusiveTotal - detailTaxInclusiveSum);
        if (taxInclusiveDifference > TOLERANCE) {
            return String.format("含税总价不一致错误：账单含税总价为%.2f，但明细含税总价之和为%.2f，差异为%.4f", 
                billTaxInclusiveTotal, detailTaxInclusiveSum, taxInclusiveDifference);
        }
        
        // 比较不含税总价
        double taxExclusiveDifference = Math.abs(billTaxExclusiveTotal - detailTaxExclusiveSum);
        if (taxExclusiveDifference > TOLERANCE) {
            return String.format("不含税总价不一致错误：账单不含税总价为%.2f，但明细不含税总价之和为%.2f，差异为%.4f", 
                billTaxExclusiveTotal, detailTaxExclusiveSum, taxExclusiveDifference);
        }
        
        // 记录调试信息（可选）
        log.debug("价格一致性验证通过 - 含税总价差异: {}, 不含税总价差异: {}", 
            taxInclusiveDifference, taxExclusiveDifference);
        
        return "验证通过";
    }
    
    /**
     * 验证字典值
     */
    private String validateDictionaryValues(Sheet sheet) {
        // 获取账单数据行（第二行）
        Row billRow = sheet.getRow(1);
        if (billRow == null) {
            return "格式错误：账单数据行不存在";
        }
        
        // 校验费用类型（第二行第一列）
        String expenseType = getCellStringValue(billRow.getCell(0)).trim();
        if (expenseType.isEmpty()) {
            return "格式错误：费用类型不能为空";
        }
        
        // 检查费用类型是否在字典中
        String expenseTypeLabel = dictService.getLabel(EXPENSE_TYPE_DICT, expenseType);
        if (expenseTypeLabel == null) {
            return String.format("字典校验错误：费用类型'%s'不在expense_type字典中", expenseType);
        }
        
        // 校验划账部门（第二行第三列）
        String department = getCellStringValue(billRow.getCell(2)).trim();
        if (department.isEmpty()) {
            return "格式错误：划账部门不能为空";
        }
        
        // 检查划账部门是否在字典中
        String departmentLabel = dictService.getLabel(EXPENSE_INSTITUDE_DICT, department);
        if (departmentLabel == null) {
            return String.format("字典校验错误：划账部门'%s'不在expense_institude字典中", department);
        }
        
        return "验证通过";
    }
    
    /**
     * 验证计费周期格式
     */
    private String validateBillingCycleFormat(Sheet sheet) {
        // 获取账单数据行（第二行）
        Row billRow = sheet.getRow(1);
        if (billRow == null) {
            return "格式错误：账单数据行不存在";
        }
        
        // 获取计费周期（第二行第二列）
        String billingCycle = getCellStringValue(billRow.getCell(1)).trim();
        if (billingCycle.isEmpty()) {
            return "格式错误：计费周期不能为空";
        }
        
        // 验证计费周期格式
        if (!isValidBillingCycle(billingCycle)) {
            return String.format("计费周期格式错误：'%s'不符合类似202505或202501-06的格式要求", billingCycle);
        }
        
        return "验证通过";
    }
    
    /**
     * 验证明细价格计算
     */
    private String validateDetailPriceCalculation(Sheet sheet) {
        // 检查所有明细行的基本数据
        for (int rowIndex = 3; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null || isEmptyRow(row)) {
                continue; // 跳过空行
            }
            
            // 获取明细数据
            double quantity = getCellNumericValue(row.getCell(7));          // 数量（第8列）
            
            // 检查数量是否为正数
            if (quantity <= 0) {
                return String.format("明细数据错误：第%d行数量必须大于0", rowIndex + 1);
            }
            
            // 注意：已去除含税单价乘数量是否等于单行含税价、不含税单价乘数量是否等于单行不含税价的校验
        }
        
        return "验证通过";
    }
    
    /**
     * 验证计费周期格式是否正确
     */
    private boolean isValidBillingCycle(String billingCycle) {
        if (billingCycle == null || billingCycle.trim().isEmpty()) {
            return false;
        }
        
        // 单月格式：202505
        if (billingCycle.matches("^\\d{6}$")) {
            return isValidYearMonth(billingCycle);
        }
        
        // 范围格式：202501-06
        if (billingCycle.matches("^\\d{6}-\\d{2}$")) {
            String[] parts = billingCycle.split("-");
            String startYearMonth = parts[0];
            String endMonth = parts[1];
            
            // 验证起始年月
            if (!isValidYearMonth(startYearMonth)) {
                return false;
            }
            
            // 验证结束月份
            int endMonthInt = Integer.parseInt(endMonth);
            if (endMonthInt < 1 || endMonthInt > 12) {
                return false;
            }
            
            // 验证结束月份不能小于起始月份
            int startMonth = Integer.parseInt(startYearMonth.substring(4, 6));
            if (endMonthInt < startMonth) {
                return false;
            }
            
            return true;
        }
        
        return false;
    }
    
    /**
     * 验证年月格式是否正确
     */
    private boolean isValidYearMonth(String yearMonth) {
        if (yearMonth == null || yearMonth.length() != 6) {
            return false;
        }
        
        try {
            int year = Integer.parseInt(yearMonth.substring(0, 4));
            int month = Integer.parseInt(yearMonth.substring(4, 6));
            
            // 验证年份范围（2000-2099）
            if (year < 2000 || year > 2099) {
                return false;
            }
            
            // 验证月份范围（1-12）
            if (month < 1 || month > 12) {
                return false;
            }
            
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    @Override
    public String parseExcelFile(MultipartFile file, BillUploadDTO uploadDTO) {
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            int totalSheets = workbook.getNumberOfSheets();
            log.info("Excel文件包含{}个Sheet", totalSheets);
            
            // 校验用户输入的账单数量与实际Sheet数量是否一致
            Integer userInputBillCount = uploadDTO.getBillCount();
            if (userInputBillCount == null || userInputBillCount != totalSheets) {
                String message = String.format("账单数量不匹配！用户输入的账单数量为%d，但Excel文件实际包含%d个Sheet。请确保账单数量与Excel文件中的Sheet数量一致。", 
                    userInputBillCount == null ? 0 : userInputBillCount, totalSheets);
                log.warn(message);
                return message;
            }
            log.info("账单数量校验通过：用户输入{}，实际文件{}个Sheet", userInputBillCount, totalSheets);
            
            // 构建JSON数据
            ObjectMapper objectMapper = new ObjectMapper();
            ObjectNode rootNode = objectMapper.createObjectNode();
            
            // 添加账单元数据
            ObjectNode metadataNode = rootNode.putObject("账单元数据");
            metadataNode.put("账单数量", totalSheets);
            metadataNode.put("发布人", uploadDTO.getPublisher());
            
            // 处理每个Sheet
            for (int sheetIndex = 0; sheetIndex < totalSheets; sheetIndex++) {
                Sheet sheet = workbook.getSheetAt(sheetIndex);
                String sheetName = sheet.getSheetName();
                log.info("正在处理Sheet：{}", sheetName);
                
                ObjectNode billNode = rootNode.putObject("账单" + (sheetIndex + 1));
                processSheetData(sheet, billNode, objectMapper);
            }
            
            // 转换为JSON字符串
            String jsonData = objectMapper.writeValueAsString(rootNode);
            log.info("生成的JSON数据: {}", jsonData);
            
            // 发送数据到receive接口
            String sendResult = sendDataToReceive(jsonData);
            
            return String.format("文件处理成功！共处理%d个Sheet，数据发送结果：%s", totalSheets, sendResult);
            
        } catch (IOException e) {
            log.error("读取Excel文件时发生错误", e);
            return "读取Excel文件失败：" + e.getMessage();
        } catch (Exception e) {
            log.error("解析Excel文件时发生错误", e);
            return "解析Excel文件失败：" + e.getMessage();
        }
    }
    
    /**
     * 格式化价格值，限制小数位数为2位并使用四舍五入
     * 
     * @param value 原始价格值
     * @return 格式化后的价格字符串，最多保留2位小数
     */
    private String formatPriceValue(double value) {
        return BigDecimal.valueOf(value)
                .setScale(2, RoundingMode.HALF_UP)
                .toPlainString();
    }
    
    /**
     * 处理单个Sheet的数据
     */
    private void processSheetData(Sheet sheet, ObjectNode billNode, ObjectMapper objectMapper) {
        // 处理账单相关数据（第二行）
        Row billDataRow = sheet.getRow(1);
        ObjectNode billDataNode = billNode.putObject("账单相关数据");
        billDataNode.put("费用类型", getCellStringValue(billDataRow.getCell(0)));
        billDataNode.put("计费周期", getCellStringValue(billDataRow.getCell(1)));
        billDataNode.put("划账部门", getCellStringValue(billDataRow.getCell(2)));
        // 对于价格数据，使用数值方法读取，然后格式化为字符串，限制小数位数为2位
        double taxInclusiveTotal = getCellNumericValue(billDataRow.getCell(3));
        double taxExclusiveTotal = getCellNumericValue(billDataRow.getCell(4));
        billDataNode.put("含税总价", formatPriceValue(taxInclusiveTotal));
        billDataNode.put("不含税总价", formatPriceValue(taxExclusiveTotal));
        
        // 处理费用明细（第四行开始）
        ArrayNode expenseDetailsNode = billNode.putArray("费用明细");
        for (int rowIndex = 3; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null || isEmptyRow(row)) {
                continue; // 跳过空行
            }
            
            ObjectNode detailNode = expenseDetailsNode.addObject();
            detailNode.put("费用名称", getCellStringValue(row.getCell(0)));
            detailNode.put("编号", getCellStringValue(row.getCell(1)));
            detailNode.put("品牌", getCellStringValue(row.getCell(2)));
            detailNode.put("具体规格", getCellStringValue(row.getCell(3)));
            detailNode.put("备注", getCellStringValue(row.getCell(4)));
            detailNode.put("费用变动情况", getCellStringValue(row.getCell(5)));
            detailNode.put("划账部门", getCellStringValue(row.getCell(6)));
            detailNode.put("数量", (int) getCellNumericValue(row.getCell(7)));
            // 对于价格数据，使用formatPriceValue方法限制小数位数为2位
            detailNode.put("含税单价", formatPriceValue(getCellNumericValue(row.getCell(8))));
            detailNode.put("不含税单价", formatPriceValue(getCellNumericValue(row.getCell(9))));
            detailNode.put("含税单行总价", formatPriceValue(getCellNumericValue(row.getCell(10))));
            detailNode.put("不含税单行总价", formatPriceValue(getCellNumericValue(row.getCell(11))));
        }
    }
    
    @Override
    public String sendDataToReceive(String jsonData) {
        HttpURLConnection connection = null;
        try {
            // 构建完整的请求URL
            String requestUrl = receiveBaseUrl + receivePath;
            
            log.info("=================== 发送数据到receive接口 ===================");
            log.info("请求方法: POST");
            log.info("请求URL: {}", requestUrl);
            
            // 创建URL对象
            URL url = new URL(requestUrl);
            
            // 打开连接
            connection = (HttpURLConnection) url.openConnection();
            
            // 设置请求方法
            connection.setRequestMethod("POST");
            
            // 设置请求头
            connection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
            connection.setRequestProperty("Accept", "application/json");
            
            // 启用输入输出流
            connection.setDoOutput(true);
            connection.setDoInput(true);
            
            // 设置连接和读取超时
            connection.setConnectTimeout(15000);
            connection.setReadTimeout(30000);
            
            // 写入请求体
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonData.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
                os.flush();
            }
            
            // 获取响应码
            int responseCode = connection.getResponseCode();
            log.info("响应状态码: {}", responseCode);
            
            // 读取响应
            StringBuilder response = new StringBuilder();
            InputStream inputStream = null;
            try {
                inputStream = responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream();
                try (BufferedReader br = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        response.append(responseLine.trim());
                    }
                }
            } catch (Exception e) {
                log.error("读取响应失败", e);
            }
            
            String responseBody = response.toString();
            log.info("响应内容: {}", responseBody);
            
            // 解析响应内容
            try {
                ObjectMapper mapper = new ObjectMapper();
                Map<String, Object> responseMap = mapper.readValue(responseBody, Map.class);
                Integer code = responseMap.get("code") instanceof Number ? ((Number) responseMap.get("code")).intValue() : null;
                String msg = responseMap.get("msg") != null ? responseMap.get("msg").toString() : "未知错误";
                
                if (code != null && code == 0) {
                    log.info("数据发送成功");
                    return "数据发送成功：" + msg;
                } else {
                    log.error("数据发送失败：{}", msg);
                    return "数据发送失败：" + msg;
                }
            } catch (Exception e) {
                log.error("解析响应失败", e);
                return "数据发送失败：响应解析错误";
            }
            
        } catch (Exception e) {
            log.error("发送数据过程中发生异常", e);
            return "数据发送异常：" + e.getMessage();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
            log.info("===============================================");
        }
    }
    
    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 对于数字，如果是整数则不显示小数点
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    // 创建公式计算器
                    FormulaEvaluator evaluator = cell.getSheet().getWorkbook().getCreationHelper().createFormulaEvaluator();
                    // 计算公式结果
                    CellValue cellValue = evaluator.evaluate(cell);
                    
                    switch (cellValue.getCellType()) {
                        case STRING:
                            return cellValue.getStringValue().trim();
                        case NUMERIC:
                            double numericValue = cellValue.getNumberValue();
                            if (numericValue == (long) numericValue) {
                                return String.valueOf((long) numericValue);
                            } else {
                                return String.valueOf(numericValue);
                            }
                        case BOOLEAN:
                            return String.valueOf(cellValue.getBooleanValue());
                        default:
                            return "";
                    }
                } catch (Exception e) {
                    log.warn("无法计算公式结果，返回公式文本：{}", cell.getCellFormula());
                    return cell.getCellFormula();
                }
            default:
                return "";
        }
    }
    
    /**
     * 获取单元格数值
     */
    private double getCellNumericValue(Cell cell) {
        if (cell == null) {
            return 0.0;
        }
        
        switch (cell.getCellType()) {
            case NUMERIC:
                return cell.getNumericCellValue();
            case STRING:
                try {
                    return Double.parseDouble(cell.getStringCellValue().trim());
                } catch (NumberFormatException e) {
                    log.warn("无法解析数字：{}", cell.getStringCellValue());
                    return 0.0;
                }
            case FORMULA:
                try {
                    // 创建公式计算器
                    FormulaEvaluator evaluator = cell.getSheet().getWorkbook().getCreationHelper().createFormulaEvaluator();
                    // 计算公式结果
                    CellValue cellValue = evaluator.evaluate(cell);
                    
                    if (cellValue.getCellType() == CellType.NUMERIC) {
                        return cellValue.getNumberValue();
                    } else if (cellValue.getCellType() == CellType.STRING) {
                        try {
                            return Double.parseDouble(cellValue.getStringValue().trim());
                        } catch (NumberFormatException e) {
                            log.warn("无法解析公式结果为数字：{}", cellValue.getStringValue());
                            return 0.0;
                        }
                    }
                    return 0.0;
                } catch (Exception e) {
                    log.warn("无法计算公式结果：{}", cell.getCellFormula());
                    return 0.0;
                }
            default:
                return 0.0;
        }
    }
    
    /**
     * 检查是否为空行
     */
    private boolean isEmptyRow(Row row) {
        for (int cellIndex = 0; cellIndex < row.getLastCellNum(); cellIndex++) {
            Cell cell = row.getCell(cellIndex);
            if (cell != null && !getCellStringValue(cell).isEmpty()) {
                return false;
            }
        }
        return true;
    }
    
    @Override
    public String validateBillCount(MultipartFile file, Integer expectedCount) {
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            int actualCount = workbook.getNumberOfSheets();
            
            if (expectedCount == null || expectedCount != actualCount) {
                String message = String.format("账单数量不匹配！预期账单数量为%d，但Excel文件实际包含%d个Sheet", 
                    expectedCount == null ? 0 : expectedCount, actualCount);
                log.warn(message);
                return message;
            }
            
            log.info("账单数量校验通过：预期{}，实际{}个Sheet", expectedCount, actualCount);
            return "验证通过";
            
        } catch (IOException e) {
            log.error("读取Excel文件时发生错误", e);
            return "读取Excel文件失败：" + e.getMessage();
        } catch (Exception e) {
            log.error("验证账单数量时发生错误", e);
            return "验证账单数量失败：" + e.getMessage();
        }
    }
}